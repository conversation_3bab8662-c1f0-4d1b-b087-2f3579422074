
"use client";

import type { <PERSON><PERSON>lert } from "@/lib/types";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Trash2, Edit3, BellOff, BellRing, MoreHorizontal, CheckCircle, Info } from "lucide-react";
import { formatDistanceToNow, parseISO, format } from "date-fns";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

interface ActiveAlertsListProps {
  alerts: PriceAlert[];
  onDeleteAlert: (alertId: string) => void;
  onToggleAlertStatus: (alertId: string, currentStatus: PriceAlert['status']) => void;
}

export function ActiveAlertsList({ alerts, onDeleteAlert, onToggleAlertStatus }: ActiveAlertsListProps) {
  if (!alerts || alerts.length === 0) {
    return (
      <Card className="card-interactive">
        <CardHeader>
          <CardTitle>Price Alerts</CardTitle>
           <CardDescription>No price alerts have been created yet.</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-center py-8">You have no price alerts.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="card-interactive">
      <CardHeader>
        <CardTitle>Manage Price Alerts</CardTitle>
        <CardDescription>Review, modify, or delete your existing price notifications.</CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Asset</TableHead>
              <TableHead>Condition</TableHead>
              <TableHead className="text-right">Target Price</TableHead>
              <TableHead className="hidden md:table-cell">Details</TableHead>
              <TableHead className="text-center">Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {alerts.map((alert) => (
              <TableRow key={alert.id} className="hover:bg-muted/50">
                <TableCell>
                  <div className="font-medium">{alert.symbol.replace('USDT', '').replace('BUSD', '')}</div>
                  <div className="text-xs text-muted-foreground">{alert.symbol}</div>
                </TableCell>
                <TableCell>
                  Price is <span className="font-semibold">{alert.condition}</span>
                </TableCell>
                <TableCell className="text-right font-mono">${alert.targetPrice?.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 8}) || 'N/A'}</TableCell>
                <TableCell className="hidden md:table-cell text-xs">
                  <div>Created: {formatDistanceToNow(parseISO(alert.createdAt), { addSuffix: true })}</div>
                  {alert.status === 'triggered' && alert.triggeredAt && (
                    <div className="text-primary/80 mt-0.5">
                      Triggered: {format(parseISO(alert.triggeredAt), "dd MMM, HH:mm")}
                    </div>
                  )}
                </TableCell>
                <TableCell className="text-center">
                  <Badge
                    variant={alert.status === 'active' ? 'default' : alert.status === 'triggered' ? 'secondary' : 'outline'}
                    className={
                      alert.status === 'active' ? 'bg-green-600/20 text-green-400 border-green-600/30' :
                      alert.status === 'triggered' ? 'bg-primary/20 text-primary border-primary/30 flex items-center gap-1' : 
                      'bg-gray-600/20 text-gray-400 border-gray-600/30'
                    }
                  >
                    {alert.status === 'triggered' && <CheckCircle className="h-3 w-3"/>}
                    {alert.status.charAt(0).toUpperCase() + alert.status.slice(1)}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => onToggleAlertStatus(alert.id, alert.status)}>
                        {(alert.status === 'active' || alert.status === 'triggered') ? <BellOff className="mr-2 h-4 w-4" /> : <BellRing className="mr-2 h-4 w-4" />}
                        {(alert.status === 'active' || alert.status === 'triggered') ? 'Deactivate' : 'Activate'}
                      </DropdownMenuItem>
                       <DropdownMenuItem 
                        onClick={() => onToggleAlertStatus(alert.id, 'cancelled')} // Allows re-activating a triggered alert
                        disabled={alert.status !== 'triggered'}
                        className={alert.status === 'triggered' ? "" : "hidden"} // Show only if triggered
                      >
                        <BellRing className="mr-2 h-4 w-4" />
                        Re-activate
                      </DropdownMenuItem>
                      <DropdownMenuItem disabled> {/* Implement edit functionality later */}
                        <Edit3 className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onDeleteAlert(alert.id)} className="text-red-500 hover:!text-red-500 focus:!text-red-500">
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
